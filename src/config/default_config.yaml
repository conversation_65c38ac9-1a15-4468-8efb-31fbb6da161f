# TDXAgent 默认配置文件
# 复制此文件为 config.yaml 并根据需要修改

settings:
  # 默认获取数据的时间范围（小时）
  default_hours_to_fetch: 12
  
  # 数据存储目录
  data_directory: "TDXAgent_Data"
  
  # 日志级别 (DEBUG, INFO, WARNING, ERROR)
  log_level: "INFO"
  
  # 最大重试次数
  max_retries: 3
  
  # 并发处理数量
  max_concurrent_tasks: 3

# 平台配置
platforms:
  # Twitter/X 配置
  twitter:
    enabled: true
    # 是否使用无头模式（建议设为 false 以降低检测风险）
    headless: false
    # 随机延迟范围（秒）
    delay_range: [2, 5]
    # 最大滚动次数
    max_scrolls: 10
    # Cookie 文件路径
    cookie_file: "twitter_cookies.json"
    
  # Telegram 配置
  telegram:
    enabled: true
    # 从 https://my.telegram.org 获取
    api_id: "your_api_id"
    api_hash: "your_api_hash"
    # 会话文件名
    session_name: "tdxagent_session"
    # 群组黑名单（不获取这些群组的数据）
    group_blacklist:
      - "spam_group_name"
      - "unwanted_group"
    # 最大消息数量限制
    max_messages: 1000
      
  # Discord 配置
  discord:
    enabled: true
    # 模式：safe（安全模式，使用官方导出）或 experimental（实验模式，有封号风险）
    mode: "safe"
    # 官方导出数据路径
    export_path: "discord_exports"
    # 实验模式配置（仅在 mode: experimental 时生效）
    experimental:
      # Discord token（高风险，可能导致封号）
      token: ""
      # 要监控的服务器 ID 列表
      server_ids: []

# LLM 配置
llm:
  # 提供商：openai 或 gemini
  provider: "openai"
  
  # 批处理大小
  batch_size: 50
  
  # 最大 token 数量
  max_tokens: 4000
  
  # 请求超时时间（秒）
  timeout: 30
  
  # OpenAI 配置
  openai:
    api_key: "sk-your-openai-api-key"
    base_url: "https://api.openai.com/v1"
    model: "gpt-4o-mini"
    temperature: 0.7
    
  # Gemini 配置
  gemini:
    api_key: "your-gemini-api-key"
    model: "gemini-1.5-flash"
    temperature: 0.7

# 提示词模板
prompts:
  twitter: |
    请分析以下 Twitter 内容，提取 3-5 个关键话题和重要信息：
    
    内容：
    {data}
    
    请按以下格式输出：
    ## 关键话题
    1. 话题1：简要描述
    2. 话题2：简要描述
    
    ## 重要信息摘要
    - 重要信息1
    - 重要信息2
    
    ## 值得关注的内容
    - 值得关注的推文或讨论
    
  telegram: |
    请总结以下 Telegram 群聊的重要信息和讨论要点：
    
    群聊内容：
    {data}
    
    请按以下格式输出：
    ## 主要讨论话题
    1. 话题1：讨论要点
    2. 话题2：讨论要点
    
    ## 重要信息
    - 重要信息1
    - 重要信息2
    
    ## 值得关注的消息
    - 重要消息或公告
    
  discord: |
    请分析以下 Discord 讨论内容，提取核心要点和重要信息：
    
    讨论内容：
    {data}
    
    请按以下格式输出：
    ## 讨论要点
    1. 要点1：详细说明
    2. 要点2：详细说明
    
    ## 重要决定或公告
    - 重要决定1
    - 重要决定2
    
    ## 技术讨论摘要
    - 技术要点1
    - 技术要点2

# 输出配置
output:
  # 报告格式
  format: "markdown"
  
  # 报告文件名模板
  filename_template: "Summary_{platform}_{timestamp}.md"
  
  # 是否包含原文链接
  include_links: true
  
  # 是否包含媒体文件信息
  include_media: true
