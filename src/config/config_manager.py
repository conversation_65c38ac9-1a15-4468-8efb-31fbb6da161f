"""
Configuration management for TDXAgent.

This module provides a comprehensive configuration management system with:
- YAML file loading and validation
- Environment variable support
- Configuration hot-reloading
- Type validation and default values
"""

import os
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
import threading
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler


@dataclass
class PlatformConfig:
    """Configuration for a specific platform."""
    enabled: bool = False
    
    
@dataclass
class TwitterConfig(PlatformConfig):
    """Twitter/X specific configuration."""
    headless: bool = False
    delay_range: list = field(default_factory=lambda: [2, 5])
    max_scrolls: int = 10
    cookie_file: str = "twitter_cookies.json"


@dataclass
class TelegramConfig(PlatformConfig):
    """Telegram specific configuration."""
    api_id: str = ""
    api_hash: str = ""
    session_name: str = "tdxagent_session"
    group_blacklist: list = field(default_factory=list)
    max_messages: int = 1000


@dataclass
class DiscordConfig(PlatformConfig):
    """Discord specific configuration."""
    mode: str = "safe"  # safe or experimental
    export_path: str = "discord_exports"
    experimental: Dict[str, Any] = field(default_factory=dict)


@dataclass
class LLMConfig:
    """LLM provider configuration."""
    provider: str = "openai"
    batch_size: int = 50
    max_tokens: int = 4000
    timeout: int = 30
    openai: Dict[str, Any] = field(default_factory=dict)
    gemini: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AppConfig:
    """Main application configuration."""
    default_hours_to_fetch: int = 12
    data_directory: str = "TDXAgent_Data"
    log_level: str = "INFO"
    max_retries: int = 3
    max_concurrent_tasks: int = 3


class ConfigFileHandler(FileSystemEventHandler):
    """File system event handler for configuration hot-reloading."""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.last_modified = 0
        
    def on_modified(self, event):
        if event.is_directory:
            return
            
        if event.src_path == str(self.config_manager.config_path):
            # Debounce rapid file changes
            current_time = time.time()
            if current_time - self.last_modified > 1:
                self.last_modified = current_time
                self.config_manager._reload_config()


class ConfigManager:
    """
    Comprehensive configuration manager for TDXAgent.
    
    Features:
    - YAML configuration file loading
    - Environment variable override support
    - Configuration validation
    - Hot-reloading capability
    - Type-safe configuration access
    """
    
    def __init__(self, config_path: Union[str, Path] = "config.yaml"):
        self.config_path = Path(config_path)
        self.logger = logging.getLogger(__name__)
        self._config_data: Dict[str, Any] = {}
        self._lock = threading.RLock()
        self._observer: Optional[Observer] = None
        
        # Configuration objects
        self.app: AppConfig = AppConfig()
        self.twitter: TwitterConfig = TwitterConfig()
        self.telegram: TelegramConfig = TelegramConfig()
        self.discord: DiscordConfig = DiscordConfig()
        self.llm: LLMConfig = LLMConfig()
        self.prompts: Dict[str, str] = {}
        self.output: Dict[str, Any] = {}
        
        self.load_config()
        
    def load_config(self) -> None:
        """Load configuration from file and environment variables."""
        with self._lock:
            try:
                # Load from YAML file
                if self.config_path.exists():
                    with open(self.config_path, 'r', encoding='utf-8') as f:
                        self._config_data = yaml.safe_load(f) or {}
                    self.logger.info(f"Loaded configuration from {self.config_path}")
                else:
                    # Load default configuration
                    default_config_path = Path(__file__).parent / "default_config.yaml"
                    if default_config_path.exists():
                        with open(default_config_path, 'r', encoding='utf-8') as f:
                            self._config_data = yaml.safe_load(f) or {}
                        self.logger.warning(f"Config file not found, using defaults from {default_config_path}")
                    else:
                        self._config_data = {}
                        self.logger.warning("No configuration file found, using empty config")
                
                # Override with environment variables
                self._apply_env_overrides()
                
                # Parse and validate configuration
                self._parse_config()
                
                self.logger.info("Configuration loaded successfully")
                
            except Exception as e:
                self.logger.error(f"Failed to load configuration: {e}")
                raise
    
    def _apply_env_overrides(self) -> None:
        """Apply environment variable overrides to configuration."""
        env_mappings = {
            'TDXAGENT_LOG_LEVEL': ['settings', 'log_level'],
            'TDXAGENT_DATA_DIR': ['settings', 'data_directory'],
            'OPENAI_API_KEY': ['llm', 'openai', 'api_key'],
            'OPENAI_BASE_URL': ['llm', 'openai', 'base_url'],
            'GEMINI_API_KEY': ['llm', 'gemini', 'api_key'],
            'TELEGRAM_API_ID': ['platforms', 'telegram', 'api_id'],
            'TELEGRAM_API_HASH': ['platforms', 'telegram', 'api_hash'],
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value:
                self._set_nested_value(self._config_data, config_path, value)
    
    def _set_nested_value(self, data: Dict[str, Any], path: list, value: Any) -> None:
        """Set a nested dictionary value using a path list."""
        for key in path[:-1]:
            data = data.setdefault(key, {})
        data[path[-1]] = value
    
    def _parse_config(self) -> None:
        """Parse and validate the loaded configuration data."""
        # Parse app settings
        settings = self._config_data.get('settings', {})
        self.app = AppConfig(
            default_hours_to_fetch=settings.get('default_hours_to_fetch', 12),
            data_directory=settings.get('data_directory', 'TDXAgent_Data'),
            log_level=settings.get('log_level', 'INFO'),
            max_retries=settings.get('max_retries', 3),
            max_concurrent_tasks=settings.get('max_concurrent_tasks', 3)
        )
        
        # Parse platform configurations
        platforms = self._config_data.get('platforms', {})
        
        # Twitter configuration
        twitter_config = platforms.get('twitter', {})
        self.twitter = TwitterConfig(
            enabled=twitter_config.get('enabled', False),
            headless=twitter_config.get('headless', False),
            delay_range=twitter_config.get('delay_range', [2, 5]),
            max_scrolls=twitter_config.get('max_scrolls', 10),
            cookie_file=twitter_config.get('cookie_file', 'twitter_cookies.json')
        )
        
        # Telegram configuration
        telegram_config = platforms.get('telegram', {})
        self.telegram = TelegramConfig(
            enabled=telegram_config.get('enabled', False),
            api_id=telegram_config.get('api_id', ''),
            api_hash=telegram_config.get('api_hash', ''),
            session_name=telegram_config.get('session_name', 'tdxagent_session'),
            group_blacklist=telegram_config.get('group_blacklist', []),
            max_messages=telegram_config.get('max_messages', 1000)
        )
        
        # Discord configuration
        discord_config = platforms.get('discord', {})
        self.discord = DiscordConfig(
            enabled=discord_config.get('enabled', False),
            mode=discord_config.get('mode', 'safe'),
            export_path=discord_config.get('export_path', 'discord_exports'),
            experimental=discord_config.get('experimental', {})
        )
        
        # LLM configuration
        llm_config = self._config_data.get('llm', {})
        self.llm = LLMConfig(
            provider=llm_config.get('provider', 'openai'),
            batch_size=llm_config.get('batch_size', 50),
            max_tokens=llm_config.get('max_tokens', 4000),
            timeout=llm_config.get('timeout', 30),
            openai=llm_config.get('openai', {}),
            gemini=llm_config.get('gemini', {})
        )
        
        # Prompts and output configuration
        self.prompts = self._config_data.get('prompts', {})
        self.output = self._config_data.get('output', {})
    
    def validate_config(self) -> bool:
        """Validate the current configuration."""
        errors = []
        
        # Validate LLM configuration
        if self.llm.provider == 'openai':
            if not self.llm.openai.get('api_key'):
                errors.append("OpenAI API key is required when using OpenAI provider")
        elif self.llm.provider == 'gemini':
            if not self.llm.gemini.get('api_key'):
                errors.append("Gemini API key is required when using Gemini provider")
        
        # Validate Telegram configuration
        if self.telegram.enabled:
            if not self.telegram.api_id or not self.telegram.api_hash:
                errors.append("Telegram API ID and hash are required when Telegram is enabled")
        
        # Validate Discord experimental mode
        if self.discord.enabled and self.discord.mode == 'experimental':
            if not self.discord.experimental.get('token'):
                errors.append("Discord token is required for experimental mode")
        
        if errors:
            for error in errors:
                self.logger.error(f"Configuration validation error: {error}")
            return False
        
        return True
    
    def enable_hot_reload(self) -> None:
        """Enable hot-reloading of configuration file."""
        if self._observer is not None:
            return
            
        try:
            self._observer = Observer()
            event_handler = ConfigFileHandler(self)
            self._observer.schedule(
                event_handler, 
                str(self.config_path.parent), 
                recursive=False
            )
            self._observer.start()
            self.logger.info("Configuration hot-reload enabled")
        except Exception as e:
            self.logger.error(f"Failed to enable hot-reload: {e}")
    
    def disable_hot_reload(self) -> None:
        """Disable hot-reloading of configuration file."""
        if self._observer is not None:
            self._observer.stop()
            self._observer.join()
            self._observer = None
            self.logger.info("Configuration hot-reload disabled")
    
    def _reload_config(self) -> None:
        """Internal method to reload configuration."""
        try:
            self.logger.info("Reloading configuration...")
            self.load_config()
            self.logger.info("Configuration reloaded successfully")
        except Exception as e:
            self.logger.error(f"Failed to reload configuration: {e}")
    
    def get_data_directory(self) -> Path:
        """Get the configured data directory as a Path object."""
        return Path(self.app.data_directory)
    
    def get_platform_config(self, platform: str) -> Optional[PlatformConfig]:
        """Get configuration for a specific platform."""
        platform_configs = {
            'twitter': self.twitter,
            'telegram': self.telegram,
            'discord': self.discord
        }
        return platform_configs.get(platform.lower())
    
    def is_platform_enabled(self, platform: str) -> bool:
        """Check if a platform is enabled."""
        config = self.get_platform_config(platform)
        return config.enabled if config else False
    
    def get_prompt(self, platform: str) -> str:
        """Get the prompt template for a platform."""
        return self.prompts.get(platform, "")
    
    def __del__(self):
        """Cleanup when the object is destroyed."""
        self.disable_hot_reload()
