"""
Base LLM provider interface for TDXAgent.

This module provides the abstract base class that all LLM providers
inherit from, ensuring consistent interface and common functionality.
"""

import asyncio
import time
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime
import logging

from ..utils.logger import TD<PERSON><PERSON>ogger, log_async_function_call


@dataclass
class LLMResponse:
    """Response from LLM provider."""
    content: str
    usage: Dict[str, Any]
    model: str
    provider: str
    timestamp: datetime
    success: bool
    error_message: Optional[str] = None
    
    @property
    def token_count(self) -> int:
        """Get total token count from usage."""
        return self.usage.get('total_tokens', 0)
    
    @property
    def input_tokens(self) -> int:
        """Get input token count."""
        return self.usage.get('prompt_tokens', 0) or self.usage.get('input_tokens', 0)
    
    @property
    def output_tokens(self) -> int:
        """Get output token count."""
        return self.usage.get('completion_tokens', 0) or self.usage.get('output_tokens', 0)


@dataclass
class LLMRequest:
    """Request to LLM provider."""
    messages: List[Dict[str, str]]
    model: str
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    system_prompt: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API calls."""
        request_dict = {
            'messages': self.messages,
            'model': self.model
        }
        
        if self.max_tokens is not None:
            request_dict['max_tokens'] = self.max_tokens
        if self.temperature is not None:
            request_dict['temperature'] = self.temperature
            
        return request_dict


class BaseLLMProvider(ABC):
    """
    Abstract base class for all LLM providers.
    
    This class provides common functionality and enforces a consistent
    interface for all LLM providers (OpenAI, Gemini, etc.).
    
    Features:
    - Rate limiting and retry logic
    - Token counting and management
    - Error handling and logging
    - Usage tracking
    - Async support
    """
    
    def __init__(self, config: Dict[str, Any], provider_name: str):
        """
        Initialize the base LLM provider.
        
        Args:
            config: Provider-specific configuration
            provider_name: Name of the provider (e.g., 'openai', 'gemini')
        """
        self.config = config
        self.provider_name = provider_name
        self.logger = TDXLogger.get_logger(f"tdxagent.llm.{provider_name}")
        
        # Rate limiting
        self.max_requests_per_minute = config.get('max_requests_per_minute', 60)
        self.max_tokens_per_minute = config.get('max_tokens_per_minute', 100000)
        self.request_timeout = config.get('timeout', 30)
        
        # Retry configuration
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 1.0)
        
        # Usage tracking
        self._request_count = 0
        self._token_count = 0
        self._last_request_time = 0.0
        self._request_times: List[float] = []
        self._token_usage_times: List[tuple[float, int]] = []
        
        # Model configuration
        self.default_model = config.get('model', '')
        self.default_max_tokens = config.get('max_tokens', 4000)
        self.default_temperature = config.get('temperature', 0.7)
        
        self.logger.info(f"Initialized {provider_name} LLM provider")
    
    @abstractmethod
    async def _make_request(self, request: LLMRequest) -> LLMResponse:
        """
        Make a request to the LLM provider.
        
        Args:
            request: LLM request object
            
        Returns:
            LLM response object
        """
        pass
    
    @abstractmethod
    def estimate_tokens(self, text: str) -> int:
        """
        Estimate the number of tokens in a text.
        
        Args:
            text: Text to estimate tokens for
            
        Returns:
            Estimated token count
        """
        pass
    
    @abstractmethod
    def get_max_context_length(self, model: Optional[str] = None) -> int:
        """
        Get the maximum context length for a model.
        
        Args:
            model: Model name (uses default if None)
            
        Returns:
            Maximum context length in tokens
        """
        pass
    
    async def generate_response(self, prompt: str, 
                               system_prompt: Optional[str] = None,
                               model: Optional[str] = None,
                               max_tokens: Optional[int] = None,
                               temperature: Optional[float] = None) -> LLMResponse:
        """
        Generate a response from the LLM.
        
        Args:
            prompt: User prompt
            system_prompt: System prompt (optional)
            model: Model to use (optional)
            max_tokens: Maximum tokens to generate (optional)
            temperature: Temperature for generation (optional)
            
        Returns:
            LLM response
        """
        # Prepare messages
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        # Create request
        request = LLMRequest(
            messages=messages,
            model=model or self.default_model,
            max_tokens=max_tokens or self.default_max_tokens,
            temperature=temperature or self.default_temperature
        )
        
        return await self.make_request_with_retry(request)
    
    async def generate_summary(self, messages: List[Dict[str, Any]], 
                              prompt_template: str,
                              model: Optional[str] = None) -> LLMResponse:
        """
        Generate a summary of messages using a prompt template.
        
        Args:
            messages: List of message dictionaries
            prompt_template: Template with {data} placeholder
            model: Model to use (optional)
            
        Returns:
            LLM response with summary
        """
        # Format messages for prompt
        formatted_messages = self.format_messages_for_prompt(messages)
        
        # Create prompt from template
        prompt = prompt_template.format(data=formatted_messages)
        
        return await self.generate_response(prompt, model=model)
    
    def format_messages_for_prompt(self, messages: List[Dict[str, Any]]) -> str:
        """
        Format messages for inclusion in prompts.
        
        Args:
            messages: List of message dictionaries
            
        Returns:
            Formatted string representation of messages
        """
        formatted_lines = []
        
        for msg in messages:
            author = msg.get('author', {}).get('name', 'Unknown')
            content = msg.get('content', {}).get('text', '')
            timestamp = msg.get('metadata', {}).get('posted_at', '')
            platform = msg.get('platform', '')
            
            # Format timestamp for readability
            if timestamp:
                try:
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    timestamp_str = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    timestamp_str = timestamp
            else:
                timestamp_str = 'Unknown time'
            
            # Create formatted line
            line = f"[{timestamp_str}] {author} ({platform}): {content}"
            formatted_lines.append(line)
        
        return '\n'.join(formatted_lines)
    
    async def rate_limit_check(self, estimated_tokens: int = 0) -> None:
        """
        Check and enforce rate limits.
        
        Args:
            estimated_tokens: Estimated tokens for the request
        """
        current_time = time.time()
        
        # Clean old request times (older than 1 minute)
        cutoff_time = current_time - 60
        self._request_times = [t for t in self._request_times if t > cutoff_time]
        self._token_usage_times = [(t, tokens) for t, tokens in self._token_usage_times if t > cutoff_time]
        
        # Check request rate limit
        if len(self._request_times) >= self.max_requests_per_minute:
            wait_time = 60 - (current_time - self._request_times[0])
            if wait_time > 0:
                self.logger.info(f"Rate limit reached, waiting {wait_time:.2f}s")
                await asyncio.sleep(wait_time)
        
        # Check token rate limit
        current_token_usage = sum(tokens for _, tokens in self._token_usage_times)
        if current_token_usage + estimated_tokens > self.max_tokens_per_minute:
            # Find when we can make the request
            required_tokens = estimated_tokens
            for t, tokens in sorted(self._token_usage_times):
                if current_token_usage - tokens + required_tokens <= self.max_tokens_per_minute:
                    wait_time = 60 - (current_time - t)
                    if wait_time > 0:
                        self.logger.info(f"Token rate limit reached, waiting {wait_time:.2f}s")
                        await asyncio.sleep(wait_time)
                    break
    
    async def make_request_with_retry(self, request: LLMRequest) -> LLMResponse:
        """
        Make a request with retry logic and rate limiting.
        
        Args:
            request: LLM request
            
        Returns:
            LLM response
        """
        # Estimate tokens for rate limiting
        estimated_tokens = sum(self.estimate_tokens(msg['content']) for msg in request.messages)
        estimated_tokens += request.max_tokens or self.default_max_tokens
        
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                # Apply rate limiting
                await self.rate_limit_check(estimated_tokens)
                
                # Make the request
                response = await self._make_request(request)
                
                # Track usage
                current_time = time.time()
                self._request_times.append(current_time)
                self._token_usage_times.append((current_time, response.token_count))
                self._request_count += 1
                self._token_count += response.token_count
                
                if response.success:
                    self.logger.debug(f"Request successful: {response.token_count} tokens")
                    return response
                else:
                    raise Exception(response.error_message or "Request failed")
                
            except Exception as e:
                last_exception = e
                if attempt < self.max_retries:
                    wait_time = self.retry_delay * (2 ** attempt)  # Exponential backoff
                    self.logger.warning(
                        f"Request attempt {attempt + 1} failed: {e}. "
                        f"Retrying in {wait_time:.2f}s..."
                    )
                    await asyncio.sleep(wait_time)
                else:
                    self.logger.error(f"All {self.max_retries + 1} attempts failed")
        
        # Return failed response
        return LLMResponse(
            content="",
            usage={},
            model=request.model,
            provider=self.provider_name,
            timestamp=datetime.now(),
            success=False,
            error_message=str(last_exception)
        )
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """
        Get usage statistics for this provider.
        
        Returns:
            Dictionary with usage statistics
        """
        current_time = time.time()
        
        # Calculate recent usage (last hour)
        hour_ago = current_time - 3600
        recent_requests = len([t for t in self._request_times if t > hour_ago])
        recent_tokens = sum(tokens for t, tokens in self._token_usage_times if t > hour_ago)
        
        return {
            'provider': self.provider_name,
            'total_requests': self._request_count,
            'total_tokens': self._token_count,
            'recent_requests_1h': recent_requests,
            'recent_tokens_1h': recent_tokens,
            'current_rpm': len([t for t in self._request_times if t > current_time - 60]),
            'current_tpm': sum(tokens for t, tokens in self._token_usage_times if t > current_time - 60),
            'max_rpm': self.max_requests_per_minute,
            'max_tpm': self.max_tokens_per_minute
        }
    
    @log_async_function_call
    async def health_check(self) -> bool:
        """
        Perform a health check on the provider.
        
        Returns:
            True if provider is healthy, False otherwise
        """
        try:
            response = await self.generate_response(
                "Hello, this is a health check. Please respond with 'OK'.",
                max_tokens=10
            )
            return response.success and 'ok' in response.content.lower()
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return False
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}(provider={self.provider_name})"
    
    def __repr__(self) -> str:
        return (f"{self.__class__.__name__}("
                f"provider={self.provider_name}, "
                f"model={self.default_model})")
