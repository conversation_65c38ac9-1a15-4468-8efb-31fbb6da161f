# TDXAgent - Personal Information AI Assistant

TDXAgent 是一款个人信息 AI 助理，旨在自动获取用户在 Twitter/X、Telegram、Discord 三大平台的信息流，并通过 LLM 进行智能分析与总结，帮助用户节省大量信息阅读与整理的时间。

## 核心功能

- **自动化信息收集**：支持三大主流社交平台的数据获取
- **智能内容分析**：基于 LLM 的内容提炼与总结
- **模块化设计**：灵活配置，易于扩展
- **风险可控**：提供多种安全级别的数据获取方案

## 支持平台

- **Twitter/X**：Following 和 For You 页面的推文及评论（含图片）
- **Telegram**：指定群组的对话内容（支持群组过滤）
- **Discord**：服务器聊天记录（提供安全和实验模式）

## 快速开始

### 安装

```bash
# 克隆项目
git clone <repository-url>
cd TDXAgent

# 安装依赖
pip install -r requirements.txt

# 安装 Playwright 浏览器
playwright install chromium
```

### 配置

1. 复制配置模板：
```bash
cp src/config/default_config.yaml config.yaml
```

2. 编辑配置文件，填入您的 API 密钥和设置

### 运行

```bash
python src/main.py
```

## 项目结构

```
TDXAgent/
├── src/                    # 源代码
│   ├── config/            # 配置管理
│   ├── scrapers/          # 数据收集模块
│   ├── storage/           # 数据存储
│   ├── llm/               # LLM 集成
│   ├── processors/        # 数据处理
│   └── utils/             # 工具函数
├── tests/                 # 测试文件
├── docs/                  # 文档
└── TDXAgent_Data/         # 数据目录（运行时创建）
```

## 许可证

MIT License
