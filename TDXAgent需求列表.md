核心需求：
1. 需要获取三件套的个人数据
	1. 推特：获取 following 和 for you 页面指定时间范围内的所有推文数据与评论信息，包括图片
	2. telegram：获取指定时间范围内的所有群组的所有对话内容数据，可以设置群组过滤名单
	3. discord：同 telegram
	4. 通过配置灵活控制三件套的数据获取开关
2. 如果用户没有指定时间范围，那么默认获取上 n 个小时到现在的所有数据，n 可配置
3. 数据中最好能够带上跳转原文的链接（可选）
4. 数据获取完成后以高效灵活易使用的存储格式保存在本地
5. 通过程序调用 LLM 接口对以上数据进行提炼总结，三件套需要预留不同的提示词修改入口方便调整 ai 总结方案
	1. 通过参数控制一次发送多少条数据给 LLM
	2. LLM 的 apikey、baseurl 和 model 等信息可配置
	3. LLM 主要支持 openai 及其兼容性接口、gemini 接口
6. 将 ai 总结方案以 md 形式保存在本地文件中
	1. 以本次数据处理时间相关的信息作为文件名保存，方便用户查看
7. 需要足够好的模块化设计，成为一个通用的个人信息 ai 助理，帮助用户节省大量信息阅读与整理的时间和精力