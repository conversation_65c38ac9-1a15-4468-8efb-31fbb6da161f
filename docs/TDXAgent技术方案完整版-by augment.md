# TDXAgent 技术方案完整版 (2025)

## 1. 项目概述

### 1.1 项目背景
TDXAgent 是一款个人信息 AI 助理，旨在自动获取用户在 Twitter/X、Telegram、Discord 三大平台的信息流，并通过 LLM 进行智能分析与总结，帮助用户节省大量信息阅读与整理的时间。

### 1.2 核心价值
- **自动化信息收集**：支持三大主流社交平台的数据获取
- **智能内容分析**：基于 LLM 的内容提炼与总结
- **模块化设计**：灵活配置，易于扩展
- **风险可控**：提供多种安全级别的数据获取方案

## 2. 需求分析

### 2.1 功能需求
1. **数据获取**
   - Twitter/X：Following 和 For You 页面的推文及评论（含图片）
   - Telegram：指定群组的对话内容（支持群组过滤）
   - Discord：服务器聊天记录（提供安全和实验模式）
   - 支持时间范围配置，默认获取过去 n 小时数据

2. **数据处理**
   - 本地存储，格式高效灵活
   - 包含原文跳转链接
   - LLM 智能分析与总结
   - 支持批量处理和自定义提示词

3. **输出管理**
   - Markdown 格式报告
   - 时间戳文件命名
   - 模块化配置管理

### 2.2 技术需求
- 支持 OpenAI 兼容接口和 Gemini API
- 可配置的 API 参数（key、baseurl、model）
- 良好的错误处理和日志记录
- 跨平台兼容性

## 3. 技术架构设计

### 3.1 整体架构
```mermaid
graph TD
    A[用户启动] --> B[配置加载]
    B --> C[数据获取模块]
    C --> D[数据存储模块]
    D --> E[LLM处理模块]
    E --> F[报告生成模块]
    
    subgraph "数据获取层"
        C1[Twitter Scraper]
        C2[Telegram Client]
        C3[Discord Processor]
    end
    
    subgraph "存储层"
        D1[JSONL文件系统]
        D2[媒体文件存储]
    end
    
    subgraph "处理层"
        E1[LLM适配器]
        E2[提示词管理]
        E3[批处理引擎]
    end
```

### 3.2 模块设计

#### 3.2.1 配置模块
- **文件格式**：YAML
- **配置项**：平台开关、时间范围、LLM参数、提示词模板
- **热重载**：支持运行时配置更新

#### 3.2.2 数据获取模块
**Twitter/X 模块**
- **技术方案**：Playwright + Cookie 认证
- **最新调研结果**：
  - X/Twitter 在 2024-2025 年大幅限制 API 访问
  - 免费 API 几乎不可用，付费 API 价格昂贵
  - Web scraping 仍是主要可行方案
  - 需要强化反检测机制

**Telegram 模块**
- **技术方案**：Telethon 官方客户端
- **最新调研结果**：
  - Telegram API 政策相对稳定
  - Telethon 库持续维护，兼容性良好
  - 官方支持的客户端操作，风险最低

**Discord 模块**
- **技术方案**：双模式设计
- **最新调研结果**：
  - Discord 对自动化检测越来越严格
  - 官方数据导出功能仍可用（安全模式）
  - Web scraping 风险极高，可能导致永久封号

#### 3.2.3 数据存储模块
- **格式**：JSON Lines (JSONL)
- **结构**：按平台和日期分层存储
- **优势**：人类可读、易于处理、支持流式写入

#### 3.2.4 LLM 处理模块
- **支持的 API**：OpenAI 兼容接口、Gemini API
- **批处理**：可配置批次大小
- **提示词管理**：平台专属模板，支持自定义

## 4. 详细技术方案

### 4.1 Twitter/X 数据获取方案对比分析

#### 4.1.1 技术方案深度对比

**📊 效率与安全性对比表**

| 维度 | Playwright + 反检测 | twscrape 库 | 评分说明 |
|------|-------------------|-------------|----------|
| **开发效率** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | twscrape 开箱即用 |
| **运行效率** | ⭐⭐ | ⭐⭐⭐⭐ | twscrape 直接API调用 |
| **资源消耗** | ⭐⭐ | ⭐⭐⭐⭐⭐ | Playwright 需要浏览器实例 |
| **账户安全性** | ⭐⭐⭐⭐ | ⭐⭐ | twscrape 封禁风险更高 |
| **长期稳定性** | ⭐⭐⭐⭐ | ⭐⭐ | Playwright 更能适应变化 |
| **维护成本** | ⭐⭐⭐ | ⭐⭐⭐ | 都需要持续维护 |

**🔍 最新调研发现**

**twscrape 存在的问题**：
- GitHub Issue #175 显示账户存活率不稳定
- 用户普遍反映重新登录功能不可靠
- 需要购买现成账户或Cookie，增加成本和风险
- 对Twitter GraphQL API变更敏感

**Playwright 方案优势**：
- 完整浏览器环境，检测难度更高
- 使用自己的账户，风险可控
- 可根据反爬策略及时调整
- 不依赖特定API端点

#### 4.1.2 推荐技术选型
基于深度调研，强烈推荐以下技术栈：

**主推方案：Playwright + 强化反检测（推荐）**
```python
# 2025年优化的反检测配置
playwright_config = {
    "headless": False,  # 关键：使用有头模式
    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
    "viewport": {"width": 1366, "height": 768},  # 常见分辨率
    "locale": "en-US",
    "timezone_id": "America/New_York",
    "permissions": ["geolocation"],
    "extra_http_headers": {
        "Accept-Language": "en-US,en;q=0.9",
        "Accept-Encoding": "gzip, deflate, br",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate"
    }
}

# 强化人类行为模拟
async def advanced_human_simulation(page):
    # 随机鼠标移动和点击
    await page.mouse.move(random.randint(100, 800), random.randint(100, 600))
    await asyncio.sleep(random.uniform(1, 3))

    # 模拟阅读停顿
    await page.evaluate("window.scrollBy(0, Math.random() * 200)")
    await asyncio.sleep(random.uniform(2, 5))
```

**备选方案：twscrape 库（限定场景）**
- ⚠️ **风险警告**：根据最新调研，账户存活率较低
- 适用场景：短期项目、可接受账户损失
- GitHub 项目：vladkens/twscrape
- 优势：开发效率高，API简洁
- 劣势：账户封禁风险高，依赖外部账户

#### 4.1.2 实施策略
1. **Cookie 管理**：实现持久化 Cookie 存储
2. **行为模拟**：随机延迟、人类化滚动
3. **错误处理**：检测限流和封禁，自动重试
4. **数据完整性**：确保图片和链接的正确获取

### 4.2 Discord 数据获取优化

#### 4.2.1 安全模式增强
```python
class DiscordSafeMode:
    def __init__(self):
        self.export_guide = """
        Discord 数据导出步骤：
        1. 打开 Discord 设置 > 隐私与安全
        2. 点击"请求我的所有数据"
        3. 等待邮件通知（通常 1-30 天）
        4. 下载数据包并放入指定目录
        """
    
    def process_export(self, zip_path):
        # 解析官方导出的数据包
        pass
```

#### 4.2.2 实验模式警告
```python
def experimental_mode_warning():
    warning = """
    ⚠️  严重警告 ⚠️
    
    Discord 实验模式存在以下风险：
    1. 违反 Discord 服务条款
    2. 可能导致账户永久封禁
    3. 检测算法日益严格
    
    请输入 'I_UNDERSTAND_THE_RISKS' 继续：
    """
    return input(warning) == 'I_UNDERSTAND_THE_RISKS'
```

### 4.3 数据存储优化

#### 4.3.1 统一数据格式
```json
{
  "id": "unique_message_id",
  "platform": "twitter|telegram|discord",
  "author": {
    "name": "用户名",
    "id": "用户ID",
    "avatar_url": "头像链接"
  },
  "content": {
    "text": "消息内容",
    "html": "HTML格式内容",
    "media": ["媒体文件URL列表"]
  },
  "metadata": {
    "posted_at": "2025-07-14T12:30:00Z",
    "message_url": "原文链接",
    "reply_to": "回复的消息ID",
    "reactions": {"👍": 5, "❤️": 3}
  },
  "context": {
    "channel": "频道/群组名称",
    "server": "服务器名称",
    "thread": "话题名称"
  }
}
```

#### 4.3.2 存储结构
```
TDXAgent_Data/
├── config/
│   ├── config.yaml
│   └── prompts/
├── data/
│   ├── twitter/
│   │   ├── 2025-07-14.jsonl
│   │   └── media/
│   ├── telegram/
│   └── discord/
├── reports/
│   └── Summary_2025-07-14_13-30-05.md
└── logs/
    └── tdxagent.log
```

### 4.4 LLM 集成优化

#### 4.4.1 提供商适配器
```python
class LLMProvider:
    def __init__(self, config):
        self.config = config
    
    def create_client(self):
        if self.config['provider'] == 'openai':
            return OpenAIProvider(self.config['openai'])
        elif self.config['provider'] == 'gemini':
            return GeminiProvider(self.config['gemini'])
        else:
            raise ValueError(f"Unsupported provider: {self.config['provider']}")
```

#### 4.4.2 智能批处理
```python
class BatchProcessor:
    def __init__(self, batch_size, token_limit):
        self.batch_size = batch_size
        self.token_limit = token_limit
    
    def create_batches(self, messages):
        # 基于 token 数量和消息数量智能分批
        pass
```

## 5. 风险评估与缓解策略

### 5.1 技术风险

| 平台 | 风险等级 | 主要风险 | 缓解策略 |
|------|----------|----------|----------|
| Twitter/X | 中-高 | 账户限制、API变更 | Cookie复用、行为模拟、备选方案 |
| Telegram | 低 | API限流 | 官方客户端、合理频率控制 |
| Discord | 低-高 | 账户封禁（实验模式） | 默认安全模式、强警告机制 |

### 5.2 法律合规
- 仅获取用户自己的数据
- 遵循各平台服务条款
- 提供明确的风险提示
- 支持数据删除和导出

### 5.3 技术债务管理
- 定期更新依赖库
- 监控平台政策变化
- 维护测试用例
- 文档同步更新

## 6. 实施计划

### 6.1 开发阶段

**阶段一：核心框架（2周）**
- 配置系统
- 数据存储模块
- 基础 LLM 集成

**阶段二：数据获取（3周）**
- Telegram 模块（优先，风险最低）
- Discord 安全模式
- Twitter/X 模块

**阶段三：优化与测试（2周）**
- 反检测优化
- 错误处理完善
- 用户体验优化

**阶段四：文档与部署（1周）**
- 用户手册
- 安装脚本
- 示例配置

### 6.2 技术栈

**核心依赖**
```
Python 3.9+
playwright>=1.40.0
telethon>=1.34.0
openai>=1.0.0
google-generativeai>=0.3.0
pyyaml>=6.0
aiofiles>=23.0.0
```

**开发工具**
- Poetry（依赖管理）
- Black（代码格式化）
- Pytest（测试框架）
- Pre-commit（代码质量）

## 7. 配置示例

### 7.1 完整配置文件
```yaml
# TDXAgent 配置文件
settings:
  default_hours_to_fetch: 12
  data_directory: "TDXAgent_Data"
  log_level: "INFO"
  max_retries: 3

platforms:
  twitter:
    enabled: true
    headless: false
    delay_range: [2, 5]
    max_scrolls: 10
    
  telegram:
    enabled: true
    api_id: "your_api_id"
    api_hash: "your_api_hash"
    group_blacklist:
      - "spam_group"
      
  discord:
    enabled: true
    mode: "safe"  # safe | experimental
    export_path: "discord_exports"

llm:
  provider: "openai"
  batch_size: 50
  max_tokens: 4000
  
  openai:
    api_key: "sk-..."
    base_url: "https://api.openai.com/v1"
    model: "gpt-4o-mini"
    
  gemini:
    api_key: "..."
    model: "gemini-1.5-flash"

prompts:
  twitter: |
    分析以下 Twitter 内容，提取 3-5 个关键话题：
    {data}
    
  telegram: |
    总结以下 Telegram 群聊的重要信息：
    {data}
    
  discord: |
    分析以下 Discord 讨论，提取核心要点：
    {data}
```

## 8. 总结

本技术方案基于 2025 年最新的平台政策和技术环境，提供了一个安全、可靠、可扩展的个人信息 AI 助理解决方案。通过模块化设计和风险分级管理，既满足了用户的功能需求，又最大程度地降低了技术和法律风险。

**核心优势：**
1. **技术先进性**：采用最新的 web 自动化和 AI 技术
2. **风险可控性**：提供多级安全选项，用户可根据需求选择
3. **扩展性强**：模块化设计，易于添加新平台和功能
4. **用户友好**：简单配置，自动化程度高

**后续发展方向：**
1. 支持更多社交平台（Reddit、LinkedIn 等）
2. 增加实时监控功能
3. 提供 Web 界面
4. 集成更多 AI 分析功能

## 9. 详细实施指南

### 9.1 项目结构
```
TDXAgent/
├── src/
│   ├── __init__.py
│   ├── main.py                 # 主入口
│   ├── config/
│   │   ├── __init__.py
│   │   ├── config_manager.py   # 配置管理
│   │   └── default_config.yaml
│   ├── scrapers/
│   │   ├── __init__.py
│   │   ├── base_scraper.py     # 基础爬虫类
│   │   ├── twitter_scraper.py  # Twitter 爬虫
│   │   ├── telegram_scraper.py # Telegram 客户端
│   │   └── discord_scraper.py  # Discord 处理器
│   ├── storage/
│   │   ├── __init__.py
│   │   ├── jsonl_storage.py    # JSONL 存储
│   │   └── media_storage.py    # 媒体文件存储
│   ├── llm/
│   │   ├── __init__.py
│   │   ├── base_provider.py    # LLM 基础类
│   │   ├── openai_provider.py  # OpenAI 适配器
│   │   └── gemini_provider.py  # Gemini 适配器
│   ├── processors/
│   │   ├── __init__.py
│   │   ├── batch_processor.py  # 批处理器
│   │   └── report_generator.py # 报告生成器
│   └── utils/
│       ├── __init__.py
│       ├── logger.py           # 日志工具
│       ├── validators.py       # 数据验证
│       └── helpers.py          # 辅助函数
├── tests/
│   ├── __init__.py
│   ├── test_scrapers.py
│   ├── test_storage.py
│   └── test_llm.py
├── docs/
│   ├── installation.md
│   ├── configuration.md
│   └── troubleshooting.md
├── scripts/
│   ├── install.sh
│   └── setup_env.py
├── requirements.txt
├── pyproject.toml
├── README.md
└── LICENSE
```

### 9.2 核心代码实现示例

#### 9.2.1 基础爬虫类
```python
# src/scrapers/base_scraper.py
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging

class BaseScraper(ABC):
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)

    @abstractmethod
    async def scrape(self, hours_back: int = 12) -> List[Dict[str, Any]]:
        """抓取指定时间范围内的数据"""
        pass

    @abstractmethod
    async def authenticate(self) -> bool:
        """认证登录"""
        pass

    def validate_message(self, message: Dict[str, Any]) -> bool:
        """验证消息格式"""
        required_fields = ['id', 'platform', 'content', 'metadata']
        return all(field in message for field in required_fields)

    def format_message(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """格式化消息为统一格式"""
        return {
            'id': raw_data.get('id'),
            'platform': self.platform_name,
            'author': {
                'name': raw_data.get('author_name'),
                'id': raw_data.get('author_id'),
                'avatar_url': raw_data.get('avatar_url')
            },
            'content': {
                'text': raw_data.get('text'),
                'html': raw_data.get('html'),
                'media': raw_data.get('media_urls', [])
            },
            'metadata': {
                'posted_at': raw_data.get('timestamp'),
                'message_url': raw_data.get('url'),
                'reply_to': raw_data.get('reply_to'),
                'reactions': raw_data.get('reactions', {})
            },
            'context': {
                'channel': raw_data.get('channel'),
                'server': raw_data.get('server'),
                'thread': raw_data.get('thread')
            }
        }
```

#### 9.2.2 Twitter 爬虫实现
```python
# src/scrapers/twitter_scraper.py
from playwright.async_api import async_playwright, Browser, Page
import asyncio
import json
import random
from typing import List, Dict, Any
from .base_scraper import BaseScraper

class TwitterScraper(BaseScraper):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.platform_name = 'twitter'
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None

    async def authenticate(self) -> bool:
        """使用 Cookie 认证"""
        try:
            playwright = await async_playwright().start()
            self.browser = await playwright.chromium.launch(
                headless=self.config.get('headless', False),
                args=['--no-blink-features=AutomationControlled']
            )

            context = await self.browser.new_context(
                user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                viewport={'width': 1920, 'height': 1080}
            )

            # 加载保存的 Cookie
            cookie_file = 'twitter_cookies.json'
            try:
                with open(cookie_file, 'r') as f:
                    cookies = json.load(f)
                    await context.add_cookies(cookies)
            except FileNotFoundError:
                self.logger.warning("未找到 Cookie 文件，需要手动登录")

            self.page = await context.new_page()
            await self.page.goto('https://x.com/home')

            # 检查是否需要登录
            await asyncio.sleep(3)
            if 'login' in self.page.url:
                self.logger.info("需要手动登录，请在浏览器中完成登录")
                input("登录完成后按 Enter 继续...")

                # 保存 Cookie
                cookies = await context.cookies()
                with open(cookie_file, 'w') as f:
                    json.dump(cookies, f)

            return True

        except Exception as e:
            self.logger.error(f"认证失败: {e}")
            return False

    async def scrape(self, hours_back: int = 12) -> List[Dict[str, Any]]:
        """抓取 Twitter 数据"""
        if not await self.authenticate():
            return []

        messages = []

        try:
            # 抓取 Following 页面
            await self.page.goto('https://x.com/home')
            following_messages = await self._scrape_timeline('following')
            messages.extend(following_messages)

            # 抓取 For You 页面
            await self.page.goto('https://x.com/home')
            await self.page.click('[data-testid="primaryColumn"] [role="tablist"] [role="tab"]:nth-child(2)')
            for_you_messages = await self._scrape_timeline('for_you')
            messages.extend(for_you_messages)

        except Exception as e:
            self.logger.error(f"抓取失败: {e}")

        finally:
            if self.browser:
                await self.browser.close()

        return messages

    async def _scrape_timeline(self, timeline_type: str) -> List[Dict[str, Any]]:
        """抓取时间线数据"""
        messages = []
        scroll_count = 0
        max_scrolls = self.config.get('max_scrolls', 10)

        while scroll_count < max_scrolls:
            # 获取当前页面的推文
            tweets = await self.page.query_selector_all('[data-testid="tweet"]')

            for tweet in tweets:
                try:
                    message_data = await self._extract_tweet_data(tweet)
                    if message_data and self.validate_message(message_data):
                        messages.append(message_data)
                except Exception as e:
                    self.logger.warning(f"提取推文数据失败: {e}")

            # 模拟人类滚动行为
            await self._human_like_scroll()
            scroll_count += 1

            # 随机延迟
            delay = random.uniform(*self.config.get('delay_range', [2, 5]))
            await asyncio.sleep(delay)

        return messages

    async def _extract_tweet_data(self, tweet_element) -> Dict[str, Any]:
        """提取单条推文数据"""
        # 实现推文数据提取逻辑
        # 包括文本、作者、时间、媒体等信息
        pass

    async def _human_like_scroll(self):
        """模拟人类滚动行为"""
        # 随机滚动距离和速度
        scroll_distance = random.randint(300, 800)
        await self.page.evaluate(f'window.scrollBy(0, {scroll_distance})')
        await asyncio.sleep(random.uniform(0.5, 1.5))
```

#### 9.2.3 LLM 提供商基类
```python
# src/llm/base_provider.py
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional

class BaseLLMProvider(ABC):
    def __init__(self, config: Dict[str, Any]):
        self.config = config

    @abstractmethod
    async def generate_summary(self, messages: List[Dict[str, Any]],
                             prompt_template: str) -> str:
        """生成内容摘要"""
        pass

    @abstractmethod
    def estimate_tokens(self, text: str) -> int:
        """估算 token 数量"""
        pass

    def format_messages_for_prompt(self, messages: List[Dict[str, Any]]) -> str:
        """将消息格式化为提示词输入"""
        formatted = []
        for msg in messages:
            author = msg['author']['name']
            content = msg['content']['text']
            timestamp = msg['metadata']['posted_at']
            formatted.append(f"[{timestamp}] {author}: {content}")
        return '\n'.join(formatted)
```

#### 9.2.4 批处理器
```python
# src/processors/batch_processor.py
import asyncio
from typing import List, Dict, Any, Callable
import logging

class BatchProcessor:
    def __init__(self, batch_size: int = 50, token_limit: int = 4000):
        self.batch_size = batch_size
        self.token_limit = token_limit
        self.logger = logging.getLogger(__name__)

    async def process_messages(self, messages: List[Dict[str, Any]],
                             llm_provider, prompt_template: str) -> List[str]:
        """批量处理消息"""
        batches = self._create_smart_batches(messages, llm_provider)
        summaries = []

        for i, batch in enumerate(batches):
            self.logger.info(f"处理批次 {i+1}/{len(batches)}")
            try:
                summary = await llm_provider.generate_summary(batch, prompt_template)
                summaries.append(summary)

                # 避免 API 限流
                await asyncio.sleep(1)

            except Exception as e:
                self.logger.error(f"批次 {i+1} 处理失败: {e}")
                summaries.append(f"批次 {i+1} 处理失败: {str(e)}")

        return summaries

    def _create_smart_batches(self, messages: List[Dict[str, Any]],
                            llm_provider) -> List[List[Dict[str, Any]]]:
        """基于 token 数量智能分批"""
        batches = []
        current_batch = []
        current_tokens = 0

        for message in messages:
            message_text = message['content']['text']
            message_tokens = llm_provider.estimate_tokens(message_text)

            if (len(current_batch) >= self.batch_size or
                current_tokens + message_tokens > self.token_limit):
                if current_batch:
                    batches.append(current_batch)
                    current_batch = []
                    current_tokens = 0

            current_batch.append(message)
            current_tokens += message_tokens

        if current_batch:
            batches.append(current_batch)

        return batches
```

### 9.3 安装和部署脚本

#### 9.3.1 自动安装脚本
```bash
#!/bin/bash
# scripts/install.sh

echo "🚀 开始安装 TDXAgent..."

# 检查 Python 版本
python_version=$(python3 --version 2>&1 | grep -oE '[0-9]+\.[0-9]+')
required_version="3.9"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ 需要 Python 3.9 或更高版本，当前版本: $python_version"
    exit 1
fi

# 创建虚拟环境
echo "📦 创建虚拟环境..."
python3 -m venv venv
source venv/bin/activate

# 安装依赖
echo "📥 安装依赖包..."
pip install --upgrade pip
pip install -r requirements.txt

# 安装 Playwright 浏览器
echo "🌐 安装 Playwright 浏览器..."
playwright install chromium

# 创建配置文件
echo "⚙️ 创建配置文件..."
cp src/config/default_config.yaml config.yaml

# 创建数据目录
echo "📁 创建数据目录..."
mkdir -p TDXAgent_Data/{twitter,telegram,discord,reports,logs}

echo "✅ 安装完成！"
echo "📖 请编辑 config.yaml 文件配置您的参数"
echo "🚀 运行命令: python src/main.py"
```

#### 9.3.2 环境设置脚本
```python
# scripts/setup_env.py
import os
import yaml
import getpass
from pathlib import Path

def setup_environment():
    """设置环境配置"""
    print("🔧 TDXAgent 环境配置向导")
    print("=" * 40)

    config_path = Path("config.yaml")
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    else:
        config = {}

    # LLM 配置
    print("\n📡 LLM 配置")
    provider = input("选择 LLM 提供商 (openai/gemini) [openai]: ").strip() or "openai"

    if provider == "openai":
        api_key = getpass.getpass("输入 OpenAI API Key: ")
        base_url = input("输入 Base URL [https://api.openai.com/v1]: ").strip() or "https://api.openai.com/v1"
        model = input("输入模型名称 [gpt-4o-mini]: ").strip() or "gpt-4o-mini"

        config.setdefault('llm', {})['openai'] = {
            'api_key': api_key,
            'base_url': base_url,
            'model': model
        }

    # Telegram 配置
    print("\n📱 Telegram 配置")
    if input("配置 Telegram? (y/n) [y]: ").strip().lower() != 'n':
        api_id = input("输入 Telegram API ID: ")
        api_hash = getpass.getpass("输入 Telegram API Hash: ")

        config.setdefault('platforms', {})['telegram'] = {
            'enabled': True,
            'api_id': api_id,
            'api_hash': api_hash
        }

    # 保存配置
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

    print("\n✅ 配置完成！")
    print(f"📄 配置文件已保存到: {config_path}")

if __name__ == "__main__":
    setup_environment()
```

### 9.4 测试策略

#### 9.4.1 单元测试示例
```python
# tests/test_scrapers.py
import pytest
import asyncio
from unittest.mock import Mock, patch
from src.scrapers.twitter_scraper import TwitterScraper

class TestTwitterScraper:
    @pytest.fixture
    def scraper_config(self):
        return {
            'headless': True,
            'delay_range': [1, 2],
            'max_scrolls': 2
        }

    @pytest.fixture
    def twitter_scraper(self, scraper_config):
        return TwitterScraper(scraper_config)

    @pytest.mark.asyncio
    async def test_authenticate_success(self, twitter_scraper):
        with patch('playwright.async_api.async_playwright') as mock_playwright:
            # 模拟成功认证
            mock_playwright.return_value.start.return_value.chromium.launch.return_value = Mock()
            result = await twitter_scraper.authenticate()
            assert result is True

    def test_validate_message(self, twitter_scraper):
        valid_message = {
            'id': 'test_id',
            'platform': 'twitter',
            'content': {'text': 'test'},
            'metadata': {'posted_at': '2025-01-01T00:00:00Z'}
        }
        assert twitter_scraper.validate_message(valid_message) is True

        invalid_message = {'id': 'test_id'}
        assert twitter_scraper.validate_message(invalid_message) is False
```

### 9.5 监控和日志

#### 9.5.1 日志配置
```python
# src/utils/logger.py
import logging
import logging.handlers
from pathlib import Path

def setup_logger(name: str, log_file: str = None, level: str = "INFO"):
    """设置日志记录器"""
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)

    # 文件处理器
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)

        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5
        )
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)

    return logger
```

## 10. 性能优化建议

### 10.1 并发处理
- 使用 asyncio 实现异步数据获取
- 并行处理多个平台的数据
- LLM 请求的合理并发控制

### 10.2 缓存策略
- 实现智能缓存避免重复抓取
- Cookie 和会话信息持久化
- 媒体文件本地缓存

### 10.3 资源管理
- 浏览器实例的合理复用
- 内存使用监控和优化
- 临时文件清理机制

## 11. 安全考虑

### 11.1 数据安全
- API 密钥加密存储
- 本地数据加密选项
- 敏感信息脱敏处理

### 11.2 网络安全
- 代理支持（可选）
- SSL 证书验证
- 请求频率限制

### 11.3 隐私保护
- 仅处理用户自己的数据
- 提供数据删除功能
- 遵循数据保护法规
